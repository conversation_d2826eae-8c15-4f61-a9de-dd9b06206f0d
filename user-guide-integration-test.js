/**
 * 用户引导系统集成测试
 * 
 * 专门测试在主应用环境中的功能集成
 * 验证所有交互按钮和引导流程
 */

class UserGuideIntegrationTest {
    constructor() {
        this.testResults = [];
        this.testStartTime = null;
    }

    /**
     * 运行完整的集成测试
     */
    async runIntegrationTests() {
        console.log('🧪 开始运行用户引导集成测试...');
        this.testStartTime = Date.now();
        this.testResults = [];

        // 等待页面完全加载
        await this.waitForPageLoad();

        // 测试基础系统
        await this.testBasicSystem();

        // 测试目标元素
        await this.testTargetElements();

        // 测试用户菜单按钮
        await this.testUserMenuButtons();

        // 测试引导流程
        await this.testGuideFlow();

        // 测试退出登录功能
        await this.testLogoutFunctionality();

        // 输出测试结果
        this.outputIntegrationResults();

        return this.testResults;
    }

    /**
     * 等待页面加载完成
     */
    async waitForPageLoad() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }

    /**
     * 测试基础系统
     */
    async testBasicSystem() {
        const tests = [
            {
                name: '用户引导管理器可用',
                test: () => typeof UserGuide !== 'undefined' && UserGuide.isInitialized
            },
            {
                name: 'Driver.js库已加载',
                test: () => typeof Driver !== 'undefined'
            },
            {
                name: '可访问性增强器可用',
                test: () => typeof AccessibilityEnhancer !== 'undefined'
            },
            {
                name: '主应用页面可见',
                test: () => {
                    const app = document.getElementById('app');
                    return app && app.style.display !== 'none';
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult('基础系统', test.name, result, result ? '✅ 正常' : '❌ 异常');
            } catch (error) {
                this.addTestResult('基础系统', test.name, false, `❌ 错误: ${error.message}`);
            }
        }
    }

    /**
     * 测试目标元素
     */
    async testTargetElements() {
        const targetElements = [
            { selector: '.sidebar', name: '侧边栏' },
            { selector: '#ai-assistant-menu', name: 'AI助手菜单' },
            { selector: '.central-input-container', name: '中央输入框' },
            { selector: '.notification-container', name: '通知中心' },
            { selector: '#user-profile-sidebar', name: '用户资料区域' }
        ];

        for (const element of targetElements) {
            const isVisible = this.isElementVisible(element.selector);
            const exists = !!document.querySelector(element.selector);
            
            this.addTestResult(
                '目标元素',
                element.name,
                isVisible,
                exists ? (isVisible ? '✅ 可见' : '⚠️ 存在但不可见') : '❌ 不存在'
            );
        }
    }

    /**
     * 测试用户菜单按钮
     */
    async testUserMenuButtons() {
        const buttons = [
            { id: 'sidebar-user-guide-item', name: '侧边栏新手引导按钮' },
            { id: 'header-user-guide-item', name: '顶部新手引导按钮' },
            { id: 'sidebar-logout-item', name: '侧边栏退出登录按钮' },
            { id: 'logout-item', name: '顶部退出登录按钮' }
        ];

        for (const button of buttons) {
            const element = document.getElementById(button.id);
            const exists = !!element;
            const hasClickListener = exists && this.hasClickListener(element);
            
            this.addTestResult(
                '菜单按钮',
                button.name,
                exists && hasClickListener,
                exists ? (hasClickListener ? '✅ 已绑定事件' : '⚠️ 缺少事件绑定') : '❌ 元素不存在'
            );
        }
    }

    /**
     * 测试引导流程
     */
    async testGuideFlow() {
        try {
            // 测试引导步骤配置
            const steps = UserGuide.getGuideSteps();
            this.addTestResult(
                '引导流程',
                '引导步骤配置',
                steps.length > 0,
                `✅ 共 ${steps.length} 个步骤`
            );

            // 测试每个步骤的目标元素
            let validSteps = 0;
            for (let i = 0; i < steps.length; i++) {
                const step = steps[i];
                if (step.element === 'body' || this.isElementVisible(step.element)) {
                    validSteps++;
                }
            }

            this.addTestResult(
                '引导流程',
                '步骤目标元素',
                validSteps === steps.length,
                `✅ ${validSteps}/${steps.length} 个步骤的目标元素可用`
            );

            // 测试引导启动功能
            const canStart = typeof UserGuide.start === 'function';
            this.addTestResult(
                '引导流程',
                '启动功能',
                canStart,
                canStart ? '✅ 可以启动' : '❌ 启动功能不可用'
            );

        } catch (error) {
            this.addTestResult('引导流程', '流程测试', false, `❌ 错误: ${error.message}`);
        }
    }

    /**
     * 测试退出登录功能
     */
    async testLogoutFunctionality() {
        try {
            // 测试showLoginPage函数是否存在
            const showLoginPageExists = typeof showLoginPage !== 'undefined';
            this.addTestResult(
                '退出登录',
                'showLoginPage函数',
                showLoginPageExists,
                showLoginPageExists ? '✅ 函数可用' : '❌ 函数不存在'
            );

            // 测试登录页面元素是否存在
            const loginPageExists = !!document.getElementById('login-page');
            this.addTestResult(
                '退出登录',
                '登录页面元素',
                loginPageExists,
                loginPageExists ? '✅ 元素存在' : '❌ 元素不存在'
            );

        } catch (error) {
            this.addTestResult('退出登录', '功能测试', false, `❌ 错误: ${error.message}`);
        }
    }

    /**
     * 检查元素是否可见
     */
    isElementVisible(selector) {
        try {
            const element = document.querySelector(selector);
            if (!element) return false;
            
            const rect = element.getBoundingClientRect();
            const style = window.getComputedStyle(element);
            
            return rect.width > 0 && 
                   rect.height > 0 && 
                   style.display !== 'none' && 
                   style.visibility !== 'hidden' && 
                   style.opacity !== '0';
        } catch (error) {
            return false;
        }
    }

    /**
     * 检查元素是否有点击事件监听器
     */
    hasClickListener(element) {
        // 这是一个简化的检查，实际的事件监听器检测比较复杂
        // 我们通过检查元素的onclick属性或者常见的事件绑定标识
        return !!(element.onclick || 
                 element.getAttribute('onclick') ||
                 element.classList.contains('clickable') ||
                 element.style.cursor === 'pointer');
    }

    /**
     * 添加测试结果
     */
    addTestResult(category, name, passed, details) {
        this.testResults.push({
            category,
            name,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 输出集成测试结果
     */
    outputIntegrationResults() {
        const duration = Date.now() - this.testStartTime;
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        console.log(`\n🧪 用户引导集成测试完成 (耗时: ${duration}ms)`);
        console.log(`📊 测试结果: ${passed}/${total} 通过`);
        console.log('=' .repeat(60));
        
        // 按类别分组显示结果
        const categories = [...new Set(this.testResults.map(r => r.category))];
        
        categories.forEach(category => {
            console.log(`\n📁 ${category}:`);
            const categoryResults = this.testResults.filter(r => r.category === category);
            categoryResults.forEach(result => {
                console.log(`  ${result.passed ? '✅' : '❌'} ${result.name}: ${result.details}`);
            });
        });
        
        console.log('=' .repeat(60));
        
        if (passed === total) {
            console.log('🎉 所有集成测试通过！用户引导系统已准备就绪。');
        } else {
            console.log(`⚠️  ${total - passed} 个测试失败，请检查相关功能。`);
            
            // 提供修复建议
            const failedTests = this.testResults.filter(r => !r.passed);
            if (failedTests.length > 0) {
                console.log('\n🔧 修复建议:');
                failedTests.forEach(test => {
                    console.log(`  • ${test.category} - ${test.name}: ${test.details}`);
                });
            }
        }
    }

    /**
     * 手动测试用户菜单功能
     */
    async testUserMenuInteraction() {
        console.log('🖱️ 开始手动测试用户菜单交互...');
        
        // 测试侧边栏用户菜单
        const sidebarProfile = document.getElementById('sidebar-profile-clickable');
        if (sidebarProfile) {
            console.log('📱 测试侧边栏用户菜单...');
            sidebarProfile.click();
            
            setTimeout(() => {
                const sidebarMenu = document.getElementById('sidebar-dropdown-menu');
                if (sidebarMenu && sidebarMenu.classList.contains('show')) {
                    console.log('✅ 侧边栏菜单显示正常');
                    
                    // 测试新手引导按钮
                    const guideBtn = document.getElementById('sidebar-user-guide-item');
                    if (guideBtn) {
                        console.log('🎯 测试侧边栏新手引导按钮...');
                        guideBtn.click();
                    }
                } else {
                    console.log('❌ 侧边栏菜单显示异常');
                }
            }, 100);
        }
        
        // 测试顶部用户菜单
        setTimeout(() => {
            const headerAvatar = document.getElementById('user-avatar-header');
            if (headerAvatar) {
                console.log('📱 测试顶部用户菜单...');
                headerAvatar.click();
                
                setTimeout(() => {
                    const headerMenu = document.getElementById('user-dropdown-menu');
                    if (headerMenu && headerMenu.classList.contains('show')) {
                        console.log('✅ 顶部菜单显示正常');
                        
                        // 测试新手引导按钮
                        const guideBtn = document.getElementById('header-user-guide-item');
                        if (guideBtn) {
                            console.log('🎯 测试顶部新手引导按钮...');
                            guideBtn.click();
                        }
                    } else {
                        console.log('❌ 顶部菜单显示异常');
                    }
                }, 100);
            }
        }, 1000);
    }
}

// 创建全局测试实例
window.UserGuideIntegrationTest = new UserGuideIntegrationTest();

// 在开发环境中提供便捷的测试命令
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('🧪 用户引导集成测试工具已加载');
    console.log('💡 使用以下命令进行测试:');
    console.log('   UserGuideIntegrationTest.runIntegrationTests() - 运行完整集成测试');
    console.log('   UserGuideIntegrationTest.testUserMenuInteraction() - 测试用户菜单交互');
}

// 导出测试类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserGuideIntegrationTest;
}
