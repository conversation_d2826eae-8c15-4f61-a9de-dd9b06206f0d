/**
 * 用户引导系统 - 基于Driver.js的新手引导功能
 * 
 * 功能特性：
 * - 多步骤引导流程
 * - 持久化存储用户状态
 * - 响应式设计适配
 * - 可访问性支持
 * - 主题系统集成
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class UserGuideManager {
    constructor(options = {}) {
        this.options = {
            // 默认配置
            storageKey: 'user_guide_completed',
            versionKey: 'user_guide_version',
            currentVersion: '1.0.0',
            autoStart: true,
            showProgress: true,
            allowClose: true,
            overlayOpacity: 0.75,
            smoothScroll: true,
            ...options
        };

        this.driver = null;
        this.isInitialized = false;
        this.currentStep = 0;
        
        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.start = this.start.bind(this);
        this.restart = this.restart.bind(this);
        this.skip = this.skip.bind(this);
        this.reset = this.reset.bind(this);
    }

    /**
     * 初始化引导系统
     */
    async init() {
        try {
            // 检查Driver.js是否已加载
            if (typeof Driver === 'undefined') {
                console.warn('Driver.js 未加载，正在动态加载...');
                await this.loadDriverJS();
            }

            // 初始化Driver实例
            this.driver = new Driver(this.getDriverConfig());
            this.isInitialized = true;

            console.log('✅ 用户引导系统初始化成功');

            // 检查是否需要自动开始引导
            if (this.options.autoStart && this.shouldShowGuide()) {
                // 延迟启动，确保页面完全加载
                setTimeout(() => this.start(), 1000);
            }

            return true;
        } catch (error) {
            console.error('❌ 用户引导系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 动态加载Driver.js库
     */
    loadDriverJS() {
        return new Promise((resolve, reject) => {
            if (typeof Driver !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/driver.js@1.3.1/dist/driver.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);

            // 同时加载CSS
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://cdn.jsdelivr.net/npm/driver.js@1.3.1/dist/driver.min.css';
            document.head.appendChild(link);
        });
    }

    /**
     * 获取Driver.js配置
     */
    getDriverConfig() {
        return {
            className: 'user-guide-driver',
            animate: true,
            opacity: this.options.overlayOpacity,
            padding: 10,
            allowClose: this.options.allowClose,
            overlayClickNext: false,
            doneBtnText: '完成引导',
            closeBtnText: '跳过',
            nextBtnText: '下一步',
            prevBtnText: '上一步',
            showButtons: ['next', 'previous', 'close'],
            keyboardControl: true,
            smoothScroll: this.options.smoothScroll,
            
            // 事件回调
            onHighlightStarted: (element) => {
                this.onStepStart(element);
            },
            onHighlighted: (element) => {
                this.onStepHighlighted(element);
            },
            onDeselected: (element) => {
                this.onStepEnd(element);
            },
            onReset: () => {
                this.onGuideComplete();
            }
        };
    }

    /**
     * 获取引导步骤配置
     */
    getGuideSteps() {
        return [
            {
                element: 'body',
                popover: {
                    className: 'guide-step-welcome',
                    title: '👋 欢迎使用跨境运营助手！',
                    description: '我将为您介绍平台的主要功能，帮助您快速上手。整个引导大约需要2分钟。<br><br><strong>💡 提示：</strong>您可以随时按 ESC 键或点击"跳过"来结束引导。',
                    position: 'center',
                    showButtons: ['next', 'close']
                }
            },
            {
                element: '.sidebar',
                popover: {
                    className: 'guide-step-sidebar',
                    title: '📋 主导航菜单',
                    description: '这里是主要的功能导航区域，包含：<br>• <strong>仪表盘</strong> - 数据概览<br>• <strong>产品库</strong> - 商品管理<br>• <strong>建联记录</strong> - 合作跟踪<br>• <strong>AI助手</strong> - 智能分析',
                    position: 'right'
                }
            },
            {
                element: '#ai-assistant-menu',
                popover: {
                    className: 'guide-step-ai',
                    title: '🤖 AI助手 - 核心功能',
                    description: 'AI助手是平台的<strong>核心功能</strong>，可以帮您：<br>• 分析商品信息和特性<br>• 推荐合适的YouTube博主<br>• 生成专业的合作邮件<br>• 评估合作伙伴匹配度',
                    position: 'right'
                }
            },
            {
                element: '.central-input-container',
                popover: {
                    className: 'guide-step-input',
                    title: '✨ 开始分析',
                    description: '在这个输入框中<strong>粘贴商品链接</strong>或描述，AI将自动分析并推荐最适合的合作博主。<br><br>🎯 <strong>快速体验：</strong>点击"试用演示"按钮可以立即体验功能！',
                    position: 'top'
                }
            },
            {
                element: '.notification-container',
                popover: {
                    className: 'guide-step-notifications',
                    title: '🔔 通知中心',
                    description: '这里会显示重要通知：<br>• 博主回复消息<br>• 合作进展更新<br>• 视频发布通知<br>• 系统重要提醒<br><br>保持关注，不错过任何重要信息！',
                    position: 'bottom-left'
                }
            },
            {
                element: '.user-profile',
                popover: {
                    className: 'guide-step-profile',
                    title: '👤 个人中心',
                    description: '点击这里可以：<br>• 管理账户设置<br>• 配置邮箱信息<br>• 查看个人资料<br>• <strong>重新启动引导</strong>（在"新手引导"选项中）',
                    position: 'top'
                }
            },
            {
                element: 'body',
                popover: {
                    className: 'guide-step-complete',
                    title: '🎉 引导完成！',
                    description: '恭喜您已经了解了平台的主要功能！<br><br>🚀 <strong>下一步：</strong>现在可以开始使用AI助手分析您的商品了。<br><br>📚 <strong>需要帮助？</strong>在用户菜单中找到"新手引导"选项可以重新查看。',
                    position: 'center',
                    showButtons: ['close']
                }
            }
        ];
    }

    /**
     * 获取高级引导步骤（针对特定功能的详细引导）
     */
    getAdvancedGuideSteps(feature) {
        const advancedSteps = {
            'ai-assistant': [
                {
                    element: '.central-input-container',
                    popover: {
                        title: '🎯 AI助手详细使用',
                        description: '让我详细介绍AI助手的使用方法...',
                        position: 'top'
                    }
                }
                // 可以添加更多特定功能的引导步骤
            ],
            'product-analysis': [
                {
                    element: '.quick-prompt-btn[data-prompt="demo"]',
                    popover: {
                        title: '🔍 商品分析演示',
                        description: '点击这里开始商品分析演示...',
                        position: 'top'
                    }
                }
            ]
        };

        return advancedSteps[feature] || [];
    }

    /**
     * 检查元素是否存在并可见
     */
    isElementVisible(selector) {
        try {
            const element = document.querySelector(selector);
            if (!element) return false;

            const rect = element.getBoundingClientRect();
            const style = window.getComputedStyle(element);

            return rect.width > 0 &&
                   rect.height > 0 &&
                   style.display !== 'none' &&
                   style.visibility !== 'hidden' &&
                   style.opacity !== '0';
        } catch (error) {
            console.warn('检查元素可见性时出错:', selector, error);
            return false;
        }
    }

    /**
     * 等待元素出现
     */
    waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            if (this.isElementVisible(selector)) {
                resolve(document.querySelector(selector));
                return;
            }

            const observer = new MutationObserver((_, obs) => {
                if (this.isElementVisible(selector)) {
                    obs.disconnect();
                    resolve(document.querySelector(selector));
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['style', 'class']
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素 ${selector} 在 ${timeout}ms 内未出现`));
            }, timeout);
        });
    }

    /**
     * 开始引导
     */
    start() {
        if (!this.isInitialized) {
            console.warn('引导系统未初始化，正在初始化...');
            this.init().then(() => {
                if (this.isInitialized) {
                    this.start();
                }
            });
            return;
        }

        try {
            const steps = this.getGuideSteps();
            this.driver.defineSteps(steps);
            this.driver.start();
            
            // 触发开始事件
            this.triggerEvent('onStart');
            
            console.log('🚀 用户引导已开始');
        } catch (error) {
            console.error('❌ 启动引导失败:', error);
        }
    }

    /**
     * 重新开始引导
     */
    restart() {
        this.reset();
        this.start();
    }

    /**
     * 跳过引导
     */
    skip() {
        if (this.driver) {
            this.driver.reset();
        }
        this.markAsCompleted();
        this.triggerEvent('onSkip');
        console.log('⏭️ 用户跳过了引导');
    }

    /**
     * 重置引导状态
     */
    reset() {
        localStorage.removeItem(this.options.storageKey);
        localStorage.removeItem(this.options.versionKey);
        console.log('🔄 引导状态已重置');
    }

    /**
     * 检查是否应该显示引导
     */
    shouldShowGuide() {
        // 检查是否已完成引导
        const completed = localStorage.getItem(this.options.storageKey);
        const version = localStorage.getItem(this.options.versionKey);
        
        // 如果未完成或版本不匹配，则显示引导
        return !completed || version !== this.options.currentVersion;
    }

    /**
     * 标记引导为已完成
     */
    markAsCompleted() {
        localStorage.setItem(this.options.storageKey, 'true');
        localStorage.setItem(this.options.versionKey, this.options.currentVersion);
    }

    /**
     * 检查引导是否已完成
     */
    isCompleted() {
        const completed = localStorage.getItem(this.options.storageKey);
        const version = localStorage.getItem(this.options.versionKey);
        return completed === 'true' && version === this.options.currentVersion;
    }

    // 事件处理方法
    onStepStart(element) {
        this.currentStep++;
        this.triggerEvent('onStepStart', { element, step: this.currentStep });
    }

    onStepHighlighted(element) {
        // 确保高亮元素可见
        if (element && element.scrollIntoView) {
            element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center',
                inline: 'nearest'
            });
        }
        this.triggerEvent('onStepHighlighted', { element, step: this.currentStep });
    }

    onStepEnd(element) {
        this.triggerEvent('onStepEnd', { element, step: this.currentStep });
    }

    onGuideComplete() {
        this.markAsCompleted();
        this.currentStep = 0;
        this.triggerEvent('onComplete');
        console.log('✅ 用户引导已完成');
    }

    /**
     * 触发自定义事件
     */
    triggerEvent(eventName, data = {}) {
        if (typeof this.options[eventName] === 'function') {
            this.options[eventName](data);
        }
        
        // 触发DOM事件
        const event = new CustomEvent(`userguide:${eventName.toLowerCase()}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }

    /**
     * 启动特定功能的引导
     */
    startFeatureGuide(feature) {
        if (!this.isInitialized) {
            console.warn('引导系统未初始化');
            return;
        }

        const steps = this.getAdvancedGuideSteps(feature);
        if (steps.length === 0) {
            console.warn(`未找到功能 ${feature} 的引导步骤`);
            return;
        }

        try {
            this.driver.defineSteps(steps);
            this.driver.start();
            console.log(`🎯 ${feature} 功能引导已开始`);
        } catch (error) {
            console.error(`❌ 启动 ${feature} 功能引导失败:`, error);
        }
    }

    /**
     * 获取引导统计信息
     */
    getStats() {
        return {
            isCompleted: this.isCompleted(),
            version: localStorage.getItem(this.options.versionKey),
            completedAt: localStorage.getItem(this.options.storageKey + '_completed_at'),
            totalSteps: this.getGuideSteps().length,
            currentStep: this.currentStep
        };
    }

    /**
     * 设置引导完成时间
     */
    markAsCompleted() {
        localStorage.setItem(this.options.storageKey, 'true');
        localStorage.setItem(this.options.versionKey, this.options.currentVersion);
        localStorage.setItem(this.options.storageKey + '_completed_at', new Date().toISOString());
    }

    /**
     * 检查浏览器兼容性
     */
    checkCompatibility() {
        const features = {
            localStorage: typeof Storage !== 'undefined',
            customEvents: typeof CustomEvent !== 'undefined',
            mutationObserver: typeof MutationObserver !== 'undefined',
            getBoundingClientRect: typeof Element.prototype.getBoundingClientRect === 'function'
        };

        const isCompatible = Object.values(features).every(Boolean);

        if (!isCompatible) {
            console.warn('浏览器兼容性检查失败:', features);
        }

        return { isCompatible, features };
    }

    /**
     * 销毁引导实例
     */
    destroy() {
        if (this.driver) {
            this.driver.reset();
            this.driver = null;
        }
        this.isInitialized = false;
        console.log('🗑️ 用户引导系统已销毁');
    }
}

/**
 * 存储管理器 - 处理引导状态的持久化
 */
class GuideStorageManager {
    constructor(prefix = 'user_guide_') {
        this.prefix = prefix;
    }

    /**
     * 设置存储项
     */
    setItem(key, value) {
        try {
            localStorage.setItem(this.prefix + key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('存储失败:', error);
            return false;
        }
    }

    /**
     * 获取存储项
     */
    getItem(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(this.prefix + key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取存储失败:', error);
            return defaultValue;
        }
    }

    /**
     * 删除存储项
     */
    removeItem(key) {
        try {
            localStorage.removeItem(this.prefix + key);
            return true;
        } catch (error) {
            console.error('删除存储失败:', error);
            return false;
        }
    }

    /**
     * 清空所有引导相关存储
     */
    clear() {
        try {
            const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
            keys.forEach(key => localStorage.removeItem(key));
            return true;
        } catch (error) {
            console.error('清空存储失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     */
    getUsage() {
        const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
        const totalSize = keys.reduce((size, key) => {
            return size + key.length + localStorage.getItem(key).length;
        }, 0);

        return {
            keys: keys.length,
            totalSize: totalSize,
            items: keys.map(key => ({
                key: key.replace(this.prefix, ''),
                size: key.length + localStorage.getItem(key).length
            }))
        };
    }
}

/**
 * 可访问性增强器 - 提供键盘导航和屏幕阅读器支持
 */
class AccessibilityEnhancer {
    constructor(userGuide) {
        this.userGuide = userGuide;
        this.keyboardHandlers = new Map();
        this.announcements = [];
        this.isActive = false;
    }

    /**
     * 初始化可访问性功能
     */
    init() {
        this.setupKeyboardNavigation();
        this.setupScreenReaderSupport();
        this.setupFocusManagement();
        console.log('✅ 可访问性增强功能已初始化');
    }

    /**
     * 设置键盘导航
     */
    setupKeyboardNavigation() {
        const keyHandler = (event) => {
            if (!this.isActive) return;

            switch (event.key) {
                case 'Escape':
                    event.preventDefault();
                    this.userGuide.skip();
                    this.announce('引导已跳过');
                    break;
                case 'ArrowRight':
                case 'Space':
                    event.preventDefault();
                    this.simulateButtonClick('next');
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    this.simulateButtonClick('previous');
                    break;
                case 'Enter':
                    event.preventDefault();
                    this.simulateButtonClick('next');
                    break;
                case 'Tab':
                    // 允许Tab键在引导按钮间导航
                    this.manageFocusWithinPopover(event);
                    break;
            }
        };

        document.addEventListener('keydown', keyHandler);
        this.keyboardHandlers.set('main', keyHandler);
    }

    /**
     * 模拟按钮点击
     */
    simulateButtonClick(buttonType) {
        const selectors = {
            next: '.driver-popover-next-btn, .driver-popover-done-btn',
            previous: '.driver-popover-prev-btn',
            close: '.driver-popover-close-btn'
        };

        const button = document.querySelector(selectors[buttonType]);
        if (button && !button.disabled) {
            button.click();
            this.announce(`${buttonType === 'next' ? '下一步' : buttonType === 'previous' ? '上一步' : '关闭'}`);
        }
    }

    /**
     * 管理弹出框内的焦点
     */
    manageFocusWithinPopover(event) {
        const popover = document.querySelector('.driver-popover');
        if (!popover) return;

        const focusableElements = popover.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (event.shiftKey) {
            // Shift + Tab
            if (document.activeElement === firstElement) {
                event.preventDefault();
                lastElement.focus();
            }
        } else {
            // Tab
            if (document.activeElement === lastElement) {
                event.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * 设置屏幕阅读器支持
     */
    setupScreenReaderSupport() {
        // 创建屏幕阅读器公告区域
        this.createAnnouncementRegion();

        // 监听引导事件
        document.addEventListener('userguide:onstepstart', (event) => {
            const { step } = event.detail;
            this.announce(`第 ${step} 步引导开始`);
        });

        document.addEventListener('userguide:onstephighlighted', () => {
            const popover = document.querySelector('.driver-popover');
            if (popover) {
                this.enhancePopoverAccessibility(popover);
            }
        });

        document.addEventListener('userguide:oncomplete', () => {
            this.announce('用户引导已完成');
            this.isActive = false;
        });
    }

    /**
     * 创建屏幕阅读器公告区域
     */
    createAnnouncementRegion() {
        if (document.getElementById('user-guide-announcements')) return;

        const announcer = document.createElement('div');
        announcer.id = 'user-guide-announcements';
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(announcer);
    }

    /**
     * 增强弹出框的可访问性
     */
    enhancePopoverAccessibility(popover) {
        // 设置ARIA属性
        popover.setAttribute('role', 'dialog');
        popover.setAttribute('aria-modal', 'true');

        const title = popover.querySelector('.driver-popover-title');
        const description = popover.querySelector('.driver-popover-description');

        if (title) {
            title.id = title.id || 'guide-popover-title';
            popover.setAttribute('aria-labelledby', title.id);
        }

        if (description) {
            description.id = description.id || 'guide-popover-description';
            popover.setAttribute('aria-describedby', description.id);
        }

        // 设置焦点到第一个按钮
        setTimeout(() => {
            const firstButton = popover.querySelector('button');
            if (firstButton) {
                firstButton.focus();
            }
        }, 100);

        // 公告当前步骤内容
        if (title && description) {
            this.announce(`${title.textContent}. ${description.textContent}`);
        }
    }

    /**
     * 设置焦点管理
     */
    setupFocusManagement() {
        let previousFocus = null;

        document.addEventListener('userguide:onstart', () => {
            this.isActive = true;
            previousFocus = document.activeElement;
            this.announce('用户引导开始，使用方向键导航，ESC键退出');
        });

        document.addEventListener('userguide:oncomplete', () => {
            this.isActive = false;
            if (previousFocus && typeof previousFocus.focus === 'function') {
                previousFocus.focus();
            }
        });

        document.addEventListener('userguide:onskip', () => {
            this.isActive = false;
            if (previousFocus && typeof previousFocus.focus === 'function') {
                previousFocus.focus();
            }
        });
    }

    /**
     * 屏幕阅读器公告
     */
    announce(message) {
        const announcer = document.getElementById('user-guide-announcements');
        if (announcer) {
            announcer.textContent = message;
            this.announcements.push({
                message,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 检查可访问性偏好
     */
    checkAccessibilityPreferences() {
        return {
            reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
            highContrast: window.matchMedia('(prefers-contrast: high)').matches,
            screenReader: this.detectScreenReader()
        };
    }

    /**
     * 检测屏幕阅读器
     */
    detectScreenReader() {
        // 简单的屏幕阅读器检测
        return !!(
            navigator.userAgent.match(/NVDA|JAWS|VoiceOver|TalkBack/i) ||
            window.speechSynthesis ||
            window.navigator.userAgent.includes('Accessibility')
        );
    }

    /**
     * 销毁可访问性功能
     */
    destroy() {
        this.keyboardHandlers.forEach((handler) => {
            document.removeEventListener('keydown', handler);
        });
        this.keyboardHandlers.clear();

        const announcer = document.getElementById('user-guide-announcements');
        if (announcer) {
            announcer.remove();
        }

        this.isActive = false;
        console.log('🗑️ 可访问性增强功能已销毁');
    }
}

// 全局实例
window.UserGuide = new UserGuideManager();
window.GuideStorage = new GuideStorageManager();
window.AccessibilityEnhancer = new AccessibilityEnhancer(window.UserGuide);

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UserGuideManager, GuideStorageManager, AccessibilityEnhancer };
}
